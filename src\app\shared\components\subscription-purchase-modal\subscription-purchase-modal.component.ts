import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { AuthService } from '../../../core/services/auth.service';
import { ModalService } from '../../../core/services/modal.service';
import { GamePackage, GamePackageGame } from '../../../core/models/game-package.model';

export interface SubscriptionPurchaseStep {
  step: 'registration' | 'game-selection' | 'payment';
  package: GamePackage;
}

@Component({
  selector: 'app-subscription-purchase-modal',
  standalone: false,
  templateUrl: './subscription-purchase-modal.component.html',
  styleUrl: './subscription-purchase-modal.component.css'
})
export class SubscriptionPurchaseModalComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() package: GamePackage | null = null;
  @Output() modalClosed = new EventEmitter<void>();
  @Output() purchaseCompleted = new EventEmitter<void>();

  currentStep: 'registration' | 'game-selection' | 'payment' = 'registration';
  
  // Registration form
  registrationForm: FormGroup;
  verificationForm: FormGroup;
  showCodeField = false;
  registeredEmail = '';
  registeredPassword = '';
  
  // Game selection
  selectedGameIds: number[] = [];
  availableGames: GamePackageGame[] = [];
  
  // Loading states
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  
  private subscription?: Subscription;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private modalService: ModalService
  ) {
    this.registrationForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      password_confirm: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });

    this.verificationForm = this.fb.group({
      code: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });
  }

  ngOnInit(): void {
    if (this.package) {
      this.availableGames = this.package.games || [];
    }
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  closeModal(): void {
    this.resetModal();
    this.modalClosed.emit();
  }

  private resetModal(): void {
    this.currentStep = 'registration';
    this.showCodeField = false;
    this.selectedGameIds = [];
    this.errorMessage = '';
    this.successMessage = '';
    this.registrationForm.reset();
    this.verificationForm.reset();
  }

  // Registration step methods
  passwordMatchValidator(group: FormGroup) {
    const password = group.get('password');
    const confirmPassword = group.get('password_confirm');
    
    if (!password || !confirmPassword) {
      return null;
    }
    
    return password.value === confirmPassword.value ? null : { passwordMismatch: true };
  }

  isFieldInvalid(fieldName: string, form?: FormGroup): boolean {
    const targetForm = form || this.registrationForm;
    const field = targetForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string, form?: FormGroup): string {
    const targetForm = form || this.registrationForm;
    const field = targetForm.get(fieldName);
    
    if (field?.errors) {
      if (field.errors['required']) return 'Это поле обязательно';
      if (field.errors['email']) return 'Введите корректный email';
      if (field.errors['minlength']) return `Минимум ${field.errors['minlength'].requiredLength} символов`;
      if (field.errors['pattern']) return 'Введите 6-значный код';
      if (field.errors['passwordMismatch']) return 'Пароли не совпадают';
    }
    
    return '';
  }

  onRegisterClick(): void {
    if (this.registrationForm.valid && !this.isLoading) {
      this.errorMessage = '';
      this.isLoading = true;

      const credentials = this.registrationForm.value;
      this.registeredEmail = credentials.email;
      this.registeredPassword = credentials.password;

      this.authService.register(credentials).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.showCodeField = true;
          this.successMessage = 'Код подтверждения отправлен на ваш email';
        },
        error: (error) => {
          this.isLoading = false;
          this.errorMessage = error.message || 'Ошибка регистрации';
        }
      });
    }
  }

  onVerifyCode(): void {
    if (this.verificationForm.valid && !this.isLoading) {
      this.errorMessage = '';
      this.isLoading = true;

      const verificationData = {
        email: this.registeredEmail,
        code: this.verificationForm.value.code
      };

      this.authService.verifyCode(verificationData).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.automaticLogin();
        },
        error: (error) => {
          this.isLoading = false;
          this.errorMessage = error.message || 'Неверный код подтверждения';
        }
      });
    }
  }

  private automaticLogin(): void {
    const loginCredentials = {
      email: this.registeredEmail,
      password: this.registeredPassword
    };

    this.authService.login(loginCredentials).subscribe({
      next: (response) => {
        this.currentStep = 'game-selection';
        this.successMessage = '';
        this.errorMessage = '';
      },
      error: (error) => {
        this.errorMessage = 'Ошибка автоматического входа. Попробуйте войти вручную.';
      }
    });
  }

  resendCode(): void {
    if (!this.isLoading) {
      this.isLoading = true;
      this.errorMessage = '';

      this.authService.resendCode({ email: this.registeredEmail }).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.successMessage = 'Код отправлен повторно';
        },
        error: (error) => {
          this.isLoading = false;
          this.errorMessage = error.message || 'Ошибка отправки кода';
        }
      });
    }
  }

  // Game selection step methods
  onGameSelectionChange(gameId: number, event: any): void {
    if (event.target.checked) {
      if (this.selectedGameIds.length < (this.package?.max_selectable_games || 0)) {
        this.selectedGameIds.push(gameId);
      } else {
        event.target.checked = false;
        this.modalService.error('Превышен лимит', `Вы можете выбрать максимум ${this.package?.max_selectable_games} игр`);
      }
    } else {
      this.selectedGameIds = this.selectedGameIds.filter(id => id !== gameId);
    }
  }

  isGameSelected(gameId: number): boolean {
    return this.selectedGameIds.includes(gameId);
  }

  canProceedToPayment(): boolean {
    return this.selectedGameIds.length > 0;
  }

  proceedToPayment(): void {
    if (this.canProceedToPayment()) {
      this.currentStep = 'payment';
    }
  }

  // Payment step methods
  completePurchase(): void {
    // Mock payment completion
    this.modalService.success('Покупка завершена!', 'Ваша подписка активирована. Выбранные игры добавлены в вашу библиотеку.').then(() => {
      this.purchaseCompleted.emit();
      this.closeModal();
    });
  }

  // Navigation methods
  goBack(): void {
    if (this.currentStep === 'game-selection') {
      this.currentStep = 'registration';
    } else if (this.currentStep === 'payment') {
      this.currentStep = 'game-selection';
    }
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }
}
