import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { GamePackage, GamePackageGame } from '../../../core/models/game-package.model';

@Component({
  selector: 'app-tarrifs-card',
  standalone: false,
  templateUrl: './tarrifs-card.html',
  styleUrl: './tarrifs-card.css'
})
export class TarrifsCard {
  @Input() package: GamePackage | null = null;
  @Input() isAuthenticated: boolean = false;
  @Output() purchasePackage = new EventEmitter<GamePackage>();
  @Output() viewDetails = new EventEmitter<GamePackage>();

  constructor(private router: Router) {}

  onPurchasePackage(): void {
    if (this.package) {
      this.purchasePackage.emit(this.package);
    }
  }

  onViewDetails(): void {
    if (this.package) {
      this.viewDetails.emit(this.package);
    }
  }

  onGameClick(game: GamePackageGame): void {
    // Navigate to game detail page
    this.router.navigate(['/games', game.id]);
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }
}
