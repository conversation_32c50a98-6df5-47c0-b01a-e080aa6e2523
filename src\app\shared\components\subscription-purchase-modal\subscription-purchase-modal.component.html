<!-- Subscription Purchase Modal -->
<div
  *ngIf="isVisible"
  class="modal-backdrop"
>
  <div class="modal-content-container">
    <!-- Modal Content -->
    <div
      class="relative bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 border border-slate-600/50 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden modal-enter"
      (click)="$event.stopPropagation()"
    >
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-slate-600/50 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-xl font-bold text-white mb-1">
            <span *ngIf="currentStep === 'registration'">Регистрация</span>
            <span *ngIf="currentStep === 'game-selection'">Выбор игр</span>
            <span *ngIf="currentStep === 'payment'">Оплата</span>
          </h3>
          <p class="text-gray-300 text-sm">{{ package?.name }}</p>
        </div>
        <button
          (click)="closeModal()"
          class="text-gray-400 hover:text-white transition-colors p-2 hover:bg-slate-700/50 rounded-lg"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <!-- Progress Steps -->
      <div class="flex items-center mt-4 space-x-4">
        <!-- Registration Step - Hide after completion -->
        <div class="flex items-center" *ngIf="!registrationCompleted">
          <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
               [class]="currentStep === 'registration' ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'">
            1
          </div>
          <span class="ml-2 text-sm text-gray-300">Регистрация</span>
        </div>
        <div class="flex-1 h-px bg-slate-600" *ngIf="!registrationCompleted"></div>

        <!-- Game Selection Step -->
        <div class="flex items-center">
          <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
               [class]="currentStep === 'game-selection' ? 'bg-blue-600 text-white' :
                        currentStep === 'payment' ? 'bg-green-600 text-white' : 'bg-slate-600 text-gray-400'">
            {{ registrationCompleted ? '1' : '2' }}
          </div>
          <span class="ml-2 text-sm text-gray-300">Выбор игр</span>
        </div>
        <div class="flex-1 h-px bg-slate-600"></div>

        <!-- Payment Step -->
        <div class="flex items-center">
          <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
               [class]="currentStep === 'payment' ? 'bg-blue-600 text-white' : 'bg-slate-600 text-gray-400'">
            {{ registrationCompleted ? '2' : '3' }}
          </div>
          <span class="ml-2 text-sm text-gray-300">Оплата</span>
        </div>
      </div>
    </div>

    <!-- Modal Body -->
    <div class="p-6 overflow-y-auto max-h-[60vh]">
      
      <!-- Registration Step -->
      <div *ngIf="currentStep === 'registration'">
        <!-- Registration Form -->
        <form [formGroup]="registrationForm" class="space-y-4" *ngIf="!showCodeField">
          <!-- Error Message -->
          <div *ngIf="errorMessage" class="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
            <p class="text-red-300 text-sm">{{ errorMessage }}</p>
          </div>

          <!-- Email Field -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Email</label>
            <input
              type="email"
              placeholder="Введите ваш email"
              formControlName="email"
              [class]="'w-full px-3 py-2 bg-slate-700/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors text-sm ' +
                       (isFieldInvalid('email') ? 'border-red-500/50 focus:ring-red-400' : 'border-slate-500 focus:ring-blue-400')"
            />
            <div *ngIf="isFieldInvalid('email')" class="text-red-400 text-xs mt-1">
              {{ getFieldError('email') }}
            </div>
          </div>

          <!-- Password Field -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Пароль</label>
            <input
              type="password"
              placeholder="Минимум 6 символов"
              formControlName="password"
              [class]="'w-full px-3 py-2 bg-slate-700/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors text-sm ' +
                       (isFieldInvalid('password') ? 'border-red-500/50 focus:ring-red-400' : 'border-slate-500 focus:ring-blue-400')"
            />
            <div *ngIf="isFieldInvalid('password')" class="text-red-400 text-xs mt-1">
              {{ getFieldError('password') }}
            </div>
          </div>

          <!-- Password Confirmation Field -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Подтвердите пароль</label>
            <input
              type="password"
              placeholder="Повторите пароль"
              formControlName="password_confirm"
              [class]="'w-full px-3 py-2 bg-slate-700/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors text-sm ' +
                       (isFieldInvalid('password_confirm') ? 'border-red-500/50 focus:ring-red-400' : 'border-slate-500 focus:ring-blue-400')"
            />
            <div *ngIf="isFieldInvalid('password_confirm')" class="text-red-400 text-xs mt-1">
              {{ getFieldError('password_confirm') }}
            </div>
          </div>
        </form>

        <!-- Email Verification Form -->
        <form [formGroup]="verificationForm" class="space-y-4" *ngIf="showCodeField">
          <!-- Success Message -->
          <div *ngIf="successMessage" class="bg-green-500/20 border border-green-500/50 rounded-lg p-3">
            <p class="text-green-300 text-sm">{{ successMessage }}</p>
          </div>

          <!-- Error Message -->
          <div *ngIf="errorMessage" class="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
            <p class="text-red-300 text-sm">{{ errorMessage }}</p>
          </div>

          <!-- Info Message -->
          <div *ngIf="!successMessage" class="bg-blue-500/20 border border-blue-500/50 rounded-lg p-3">
            <p class="text-blue-300 text-sm">
              Код подтверждения отправлен на <strong>{{ registeredEmail }}</strong>
            </p>
          </div>

          <!-- Verification Code Field -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Код подтверждения</label>
            <input
              type="text"
              placeholder="Введите 6-значный код"
              formControlName="code"
              maxlength="6"
              [class]="'w-full px-3 py-2 bg-slate-700/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors text-center text-lg tracking-widest ' +
                       (isFieldInvalid('code', verificationForm) ? 'border-red-500/50 focus:ring-red-400' : 'border-green-500 focus:ring-green-400')"
            />
            <div *ngIf="isFieldInvalid('code', verificationForm)" class="text-red-400 text-xs mt-1">
              {{ getFieldError('code', verificationForm) }}
            </div>
          </div>

          <!-- Resend Code Button -->
          <div class="text-center">
            <button
              type="button"
              (click)="resendCode()"
              [disabled]="isLoading"
              class="text-blue-400 hover:text-blue-300 transition-colors text-sm font-medium disabled:opacity-50">
              Отправить код повторно
            </button>
          </div>
        </form>
      </div>

      <!-- Game Selection Step -->
      <div *ngIf="currentStep === 'game-selection'">
        <div class="mb-4">
          <p class="text-gray-300 text-sm mb-2">
            Выберите до {{ package?.max_selectable_games }} игр из пакета:
          </p>
          <p class="text-blue-400 text-sm">
            Выбрано: {{ selectedGameIds.length }} / {{ package?.max_selectable_games }}
          </p>
        </div>

        <!-- Error Message -->
        <div *ngIf="errorMessage" class="bg-red-500/20 border border-red-500/50 rounded-lg p-3 mb-4">
          <p class="text-red-300 text-sm">{{ errorMessage }}</p>
        </div>

        <!-- Debug Info -->
        <div *ngIf="availableGames.length === 0" class="bg-yellow-500/20 border border-yellow-500/50 rounded-lg p-3 mb-4">
          <p class="text-yellow-300 text-sm">
            Игры не загружены. Пакет: {{ package?.name }}, Игр в пакете: {{ package?.games?.length || 0 }}
          </p>
        </div>

        <div class="grid grid-cols-2 gap-4 max-h-96 overflow-y-auto mb-4">
          <label *ngFor="let game of availableGames"
                 class="game-card relative bg-slate-700/30 rounded-lg cursor-pointer overflow-hidden"
                 [class.selected]="isGameSelected(game.id)">

            <!-- Game Image -->
            <div class="game-image-container">
              <img
                *ngIf="game.cover_image"
                [src]="game.cover_image"
                [alt]="game.title"
                class="game-image w-full h-full object-cover"
                (error)="onImageError($event)"
              >
              <div
                *ngIf="!game.cover_image"
                class="w-full h-full flex items-center justify-center bg-gradient-to-br from-slate-700 to-slate-800">
                <svg class="w-12 h-12 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>

              <!-- Selection Overlay -->
              <div
                *ngIf="isGameSelected(game.id)"
                class="selection-overlay absolute inset-0 bg-blue-600/20 flex items-center justify-center">
                <div class="bg-blue-600 rounded-full p-2 shadow-lg">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Game Info -->
            <div class="p-3">
              <div class="flex items-center space-x-3">
                <input
                  type="checkbox"
                  [checked]="isGameSelected(game.id)"
                  (change)="onGameSelectionChange(game.id, $event)"
                  class="rounded border-slate-500 text-blue-600 focus:ring-blue-500 flex-shrink-0 w-4 h-4"
                >
                <div class="flex-1 min-w-0">
                  <div class="game-title text-white text-sm font-medium">{{ game.title }}</div>
                </div>
              </div>
            </div>
          </label>
        </div>

        <!-- Preview Section -->
        <div *ngIf="selectedGameIds.length > 0" class="mt-4">
          <div *ngIf="previewLoading" class="bg-slate-700/30 rounded-lg p-4 text-center">
            <div class="flex items-center justify-center space-x-2">
              <svg class="animate-spin h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span class="text-gray-300 text-sm">Обновление предварительного просмотра...</span>
            </div>
          </div>

          <div *ngIf="!previewLoading && previewData" class="bg-slate-700/30 rounded-lg p-4">
            <h4 class="text-white font-medium mb-3">Предварительный просмотр покупки</h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-300">Выбрано игр:</span>
                <span class="text-white">{{ previewData.preview.games_count }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-300">Длительность:</span>
                <span class="text-white">{{ previewData.preview.expires_in_days }} дней</span>
              </div>
              <div class="flex justify-between font-medium">
                <span class="text-gray-300">Стоимость:</span>
                <span class="text-white">{{ formatPrice(previewData.preview.total_price) }}</span>
              </div>
            </div>

            <div *ngIf="!previewData.can_purchase" class="mt-3 bg-red-500/20 border border-red-500/50 rounded p-2">
              <p class="text-red-300 text-xs">Покупка недоступна. Проверьте выбранные игры.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment Step -->
      <div *ngIf="currentStep === 'payment'">
        <!-- Error Message -->
        <div *ngIf="errorMessage" class="bg-red-500/20 border border-red-500/50 rounded-lg p-3 mb-4">
          <p class="text-red-300 text-sm">{{ errorMessage }}</p>
        </div>

        <div class="space-y-6">
          <div class="bg-slate-700/30 rounded-lg p-6">
            <h4 class="text-lg font-semibold text-white mb-4">Детали заказа</h4>

            <div class="space-y-3 text-left">
              <div class="flex justify-between">
                <span class="text-gray-300">Пакет:</span>
                <span class="text-white">{{ (previewData?.preview?.package?.name) || (package?.name) || 'Неизвестный пакет' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-300">Выбрано игр:</span>
                <span class="text-white">{{ (previewData?.preview?.games_count) || selectedGameIds.length }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-300">Длительность:</span>
                <span class="text-white">{{ (previewData?.preview?.expires_in_days) || (package?.duration_days) || 0 }} дней</span>
              </div>
              <div class="border-t border-slate-600 pt-3 flex justify-between text-lg font-semibold">
                <span class="text-gray-300">Итого:</span>
                <span class="text-white">{{ formatPrice((previewData?.preview?.total_price) || (package?.price) || '0') }}</span>
              </div>
            </div>

            <!-- Selected Games List -->
            <div *ngIf="previewData?.preview?.selected_games && (previewData?.preview?.selected_games?.length || 0) > 0" class="mt-4 pt-4 border-t border-slate-600">
              <h5 class="text-white font-medium mb-2">Выбранные игры:</h5>
              <div class="space-y-1">
                <div *ngFor="let game of (previewData?.preview?.selected_games || [])" class="text-gray-300 text-sm">
                  • {{ game.title }}
                </div>
              </div>
            </div>
          </div>

          <div class="bg-green-500/20 border border-green-500/50 rounded-lg p-4">
            <p class="text-green-300 text-sm">
              ✓ После оплаты вы получите мгновенный доступ к выбранным играм
            </p>
          </div>

          <div class="bg-blue-500/20 border border-blue-500/50 rounded-lg p-4">
            <p class="text-blue-300 text-sm">
              Это демонстрационная версия. В реальной системе здесь будет интеграция с платежной системой.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Footer -->
    <div class="border-t border-slate-600/50 p-6 bg-slate-800/50">
      <div class="flex justify-between">
        <!-- Back Button -->
        <button
          *ngIf="canGoBack()"
          (click)="goBack()"
          class="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-lg transition-colors text-sm"
        >
          Назад
        </button>
        <div *ngIf="!canGoBack()"></div>

        <!-- Action Buttons -->
        <div class="flex space-x-3">
          <!-- Registration Step Buttons -->
          <button
            *ngIf="currentStep === 'registration' && !showCodeField"
            (click)="onRegisterClick()"
            [disabled]="!registrationForm.valid || isLoading"
            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium"
          >
            <span *ngIf="!isLoading">Зарегистрироваться</span>
            <span *ngIf="isLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Отправка...
            </span>
          </button>

          <button
            *ngIf="currentStep === 'registration' && showCodeField"
            (click)="onVerifyCode()"
            [disabled]="!verificationForm.valid || isLoading"
            class="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium"
          >
            <span *ngIf="!isLoading">Подтвердить</span>
            <span *ngIf="isLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Проверка...
            </span>
          </button>

          <!-- Game Selection Step Button -->
          <button
            *ngIf="currentStep === 'game-selection'"
            (click)="proceedToPayment()"
            [disabled]="!canProceedToPayment()"
            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium flex items-center"
          >
            Продолжить
            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>

          <!-- Payment Step Button -->
          <button
            *ngIf="currentStep === 'payment'"
            (click)="completePurchase()"
            [disabled]="isLoading"
            class="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium"
          >
            <span *ngIf="!isLoading">Завершить покупку</span>
            <span *ngIf="isLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Обработка...
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
