<!-- Main Container -->
<div class="min-h-screen relative overflow-hidden">
  <!-- Gradient background for the rest of the page -->
  <div
    class="fixed inset-0"
    style="background: linear-gradient(to bottom, rgba(32,21,62,1), rgba(32,21,62,0.8), #000000)"
  ></div>
  <div class="fixed inset-0 animated-gradient opacity-15"></div>

  <!-- Content overlay to ensure proper layering -->
  <div class="relative z-10">

    <!-- Hero Section with Custom Background -->
    <section
      class="relative min-h-screen flex items-center justify-center overflow-hidden pt-20 hero-section"
    >
      <!-- Hero-specific background image -->
      <div
        class="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style="background-image: url('assets/main-image/main-bg.png')"
      ></div>
      <!-- Gradient transition overlay -->
      <div class="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-[rgba(32,21,62,1)]"></div>
      <!-- Subtle overlay to maintain readability -->
      <div class="absolute inset-0 bg-black/20"></div>

      <!-- Content -->
      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- Left Content - Full width on mobile, half width on desktop -->
          <div class="space-y-8 text-center lg:text-left lg:col-span-1 col-span-1 lg:mx-0 mx-auto max-w-3xl lg:max-w-none">
            <!-- Main Title Image -->
            <div class="flex justify-center lg:justify-start">
              <img
                src="assets/main-image/main-txt.svg"
                alt="Уникальное развлечение для гостей вашего тоя"
                class="max-w-full h-auto drop-shadow-2xl"
              />
            </div>

            <!-- Description -->
            <p
              class="text-lg md:text-xl text-gray-300 max-w-2xl mx-auto lg:mx-0 leading-relaxed text-center lg:text-left"
            >
              Интерактивные игры и развлечения, которые сделают ваше мероприятие
              незабываемым
            </p>

            <!-- Action Button -->
            <div
              class="flex justify-center lg:justify-start"
            >
              <button
                (click)="scrollToSection('how-to-play')"
                class="bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-4 py-2 sm:px-6 sm:py-2.5 rounded-full hover:from-cyan-600 hover:to-blue-700 transition-all transform hover:scale-105 font-medium text-sm sm:text-base shadow-lg retro-button-glow"
              >
                Смотреть демо
              </button>
            </div>


          </div>

          <!-- Right Content - Hidden on mobile -->
          <div class="relative hidden lg:flex justify-center lg:justify-end lg:pr-0">
            <img
              src="assets/main-image/micro-image.png"
              alt="Microphones"
              class="max-w-full h-auto relative z-10 drop-shadow-2xl transform scale-75 lg:translate-x-6"
            />

            <!-- Chaotic and faded glow effects around microphones -->
            <!-- Scattered cyan glows -->
            <div
              class="absolute -top-12 -left-6 w-10 h-10 bg-cyan-400/20 rounded-full blur-2xl animate-pulse"
              style="animation-duration: 4.2s; animation-delay: 0.3s"
            ></div>
            <div
              class="absolute -top-3 -left-12 w-6 h-6 bg-cyan-300/15 rounded-full blur-xl animate-pulse"
              style="animation-duration: 2.8s; animation-delay: 1.7s"
            ></div>
            <!-- Chaotic pink glows -->
            <div
              class="absolute -bottom-8 -right-4 w-14 h-14 bg-pink-400/18 rounded-full blur-3xl animate-pulse"
              style="animation-duration: 5.1s; animation-delay: 0.8s"
            ></div>
            <div
              class="absolute bottom-1/3 -right-10 w-8 h-8 bg-pink-300/12 rounded-full blur-2xl animate-pulse"
              style="animation-duration: 3.3s; animation-delay: 2.1s"
            ></div>
            <!-- Random purple spots -->
            <div
              class="absolute top-1/4 -left-8 w-7 h-7 bg-purple-400/16 rounded-full blur-xl animate-pulse"
              style="animation-duration: 4.7s; animation-delay: 1.2s"
            ></div>
            <div
              class="absolute top-2/3 -left-3 w-5 h-5 bg-purple-300/10 rounded-full blur-lg animate-pulse"
              style="animation-duration: 2.1s; animation-delay: 3.4s"
            ></div>
            <!-- Scattered blue accents -->
            <div
              class="absolute -top-6 right-1/3 w-9 h-9 bg-blue-400/22 rounded-full blur-2xl animate-pulse"
              style="animation-duration: 3.6s; animation-delay: 0.5s"
            ></div>
            <div
              class="absolute top-1/6 right-1/5 w-4 h-4 bg-blue-300/14 rounded-full blur-xl animate-pulse"
              style="animation-duration: 2.9s; animation-delay: 2.8s"
            ></div>
            <!-- Faded white sparkles -->
            <div
              class="absolute bottom-1/5 -left-5 w-6 h-6 bg-white/8 rounded-full blur-lg animate-pulse"
              style="animation-duration: 1.8s; animation-delay: 1.9s"
            ></div>
            <div
              class="absolute bottom-2/3 right-1/6 w-3 h-3 bg-white/6 rounded-full blur-md animate-pulse"
              style="animation-duration: 2.4s; animation-delay: 0.7s"
            ></div>
            <!-- Additional chaotic elements -->
            <div
              class="absolute top-1/2 -right-2 w-11 h-11 bg-cyan-500/14 rounded-full blur-3xl animate-pulse"
              style="animation-duration: 6.2s; animation-delay: 1.5s"
            ></div>
            <div
              class="absolute -bottom-2 left-1/4 w-5 h-5 bg-pink-500/11 rounded-full blur-2xl animate-pulse"
              style="animation-duration: 3.8s; animation-delay: 2.6s"
            ></div>
          </div>
        </div>
      </div>

      <!-- Retro Synthwave Floating Elements -->
      <div
        class="absolute top-1/4 left-10 w-24 h-24 bg-cyan-500/30 rounded-full blur-xl animate-pulse"
      ></div>
      <div
        class="absolute bottom-1/4 right-10 w-32 h-32 bg-pink-500/25 rounded-full blur-xl animate-pulse delay-1000"
      ></div>
      <div
        class="absolute top-1/2 left-1/4 w-20 h-20 bg-purple-500/20 rounded-full blur-xl animate-pulse delay-500"
      ></div>
      <div
        class="absolute top-3/4 right-1/4 w-16 h-16 bg-blue-500/25 rounded-full blur-xl animate-pulse delay-2000"
      ></div>

      <!-- Retro grid-like sparkles -->
      <div
        class="absolute top-10 right-1/3 w-2 h-2 bg-cyan-400 rounded-full animate-ping"
      ></div>
      <div
        class="absolute bottom-20 left-1/3 w-1 h-1 bg-pink-400 rounded-full animate-ping delay-1000"
      ></div>
      <div
        class="absolute top-1/3 right-10 w-1.5 h-1.5 bg-blue-400 rounded-full animate-ping delay-2000"
      ></div>

      <!-- Gentle sparkle highlights -->
      <div
        class="absolute top-16 left-1/4 w-3 h-3 bg-blue-400/60 rounded-full animate-sparkle"
        style="animation-delay: 1s"
      ></div>
      <div
        class="absolute bottom-32 right-1/5 w-2 h-2 bg-cyan-300/50 rounded-full animate-sparkle"
        style="animation-delay: 3s"
      ></div>
      <div
        class="absolute top-2/3 left-16 w-4 h-4 bg-purple-300/40 rounded-full animate-sparkle"
        style="animation-delay: 5s"
      ></div>

      <!-- Subtle glow elements -->
      <div
        class="absolute top-1/5 right-1/6 w-6 h-6 bg-blue-500/20 rounded-full blur-sm animate-glow-pulse"
        style="animation-delay: 2s"
      ></div>
      <div
        class="absolute bottom-1/5 left-1/5 w-8 h-8 bg-purple-500/15 rounded-full blur-md animate-glow-pulse"
        style="animation-delay: 4s"
      ></div>
    </section>

    <!-- Games Section -->
    <section id="games" class="relative py-24">
      <!-- Subtle background highlights for games section -->
      <div
        class="absolute top-10 left-10 w-12 h-12 bg-blue-400/10 rounded-full blur-xl animate-sparkle"
        style="animation-delay: 1s"
      ></div>
      <div
        class="absolute bottom-10 right-10 w-16 h-16 bg-purple-400/10 rounded-full blur-xl animate-sparkle"
        style="animation-delay: 3s"
      ></div>

      <div class="max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Title -->
        <div class="text-center mb-20">
          <h2
            class="text-4xl md:text-5xl lg:text-5xl font-black text-[#40A3FF] mb-6 tracking-tight"
          >
            НАШИ ИГРЫ
          </h2>
          <p
            class="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
          >
            Выберите идеальную игру для вашего мероприятия
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto mt-8 rounded-full"
          ></div>
        </div>

        <!-- Games Grid -->
        <div *ngIf="gamesLoading" class="flex justify-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>

        <div *ngIf="gamesError" class="text-center py-12">
          <p class="text-red-400 text-lg">{{ gamesError }}</p>
          <button
            (click)="loadFeaturedGames()"
            class="mt-4 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Попробовать снова
          </button>
        </div>

        <div *ngIf="!gamesLoading && !gamesError" class="space-y-8">
          <!-- Games Cards Grid -->
          <div class="games-grid-4col-centered">
            <div
              *ngFor="let game of featuredGames"
              class="relative w-full max-w-xs mx-auto bg-cover bg-center bg-no-repeat rounded-2xl
              p-4 md:p-5 flex flex-col h-auto min-h-[480px] glow-on-hover transition-all duration-300 hover:scale-105 cursor-pointer"
              style="background-image: url('assets/images/rectangle.png')"
              (click)="viewGameDetails(game.id)"
            >
              <!-- Overlay for better text readability -->
              <div class="absolute inset-0 bg-black/20 rounded-2xl"></div>

              <div class="relative z-10 text-white flex flex-col h-full">
                <!-- Game Cover Image Section -->
                <div class="w-full aspect-video rounded-xl overflow-hidden mb-3">
                  <img
                    *ngIf="game.cover_image"
                    [src]="game.cover_image"
                    [alt]="game.title"
                    class="w-full h-full object-cover rounded-xl"
                  >
                  <div
                    *ngIf="!game.cover_image"
                    class="w-full h-full bg-gradient-to-br from-slate-700 to-slate-800 rounded-xl flex items-center justify-center"
                  >
                    <svg class="w-8 h-8 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                </div>

                <!-- Content Section -->
                <div class="flex-1 flex flex-col px-4">
                  <div class="mb-6">
                    <h3 class="font-black text-xl md:text-3xl mb-1">{{ game.title }}</h3>
                    <p class="text-sm leading-relaxed text-gray-200 line-clamp-3">
                      {{ game.description }}
                    </p>
                  </div>

                  <!-- Game Features -->
                  <div class="mb-6">
                    <p class="text-sm leading-relaxed text-gray-200 mb-2">
                      Особенности:
                    </p>
                    <div class="flex flex-wrap gap-2">
                      <div
                        *ngIf="game.trial_available"
                        class="flex items-center gap-2 bg-[#2E2E2E] px-3 py-2 rounded-lg text-white text-sm"
                      >
                        <span>Пробная версия</span>
                      </div>
                      <div
                        *ngIf="game.requires_device"
                        class="flex items-center gap-2 bg-[#2E2E2E] px-3 py-2 rounded-lg text-white text-sm"
                      >
                        <img src="assets/icons/micro.svg" alt="device" class="w-4 h-4" />
                        <span>Устройство</span>
                      </div>
                      <div class="flex items-center gap-2 bg-[#2E2E2E] px-3 py-2 rounded-lg text-white text-sm">
                        <span>Игра</span>
                      </div>

                      <!-- Library Access Status -->
                      <div *ngIf="canPlay(game)"
                           class="flex items-center gap-2 bg-green-600 px-3 py-2 rounded-lg text-white text-sm">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Играть</span>
                      </div>
                      <div *ngIf="needsAccessExtension(game)"
                           class="flex items-center gap-2 bg-red-600 px-3 py-2 rounded-lg text-white text-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <span>Истёк - можете продлить</span>
                      </div>
                    </div>
                  </div>

                  <!-- Price and Action -->
                  <div class="mt-auto flex no-wrap items-center justify-between gap-1">
                    <div class="text-xl md:text-xl font-bold text-[#FFB94A]">{{ formatPrice(game.price) }}</div>
                    <button
                      (click)="addToCart(game, $event)"
                      [disabled]="isInCart(game) || canPlay(game)"
                      [class]="canPlay(game)
                        ? 'bg-green-600 text-white cursor-not-allowed py-3 px-4 w-2/3 font-bold rounded-lg'
                        : needsAccessExtension(game)
                        ? 'bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 w-2/3 font-bold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl'
                        : isInCart(game)
                        ? 'bg-gray-600 text-gray-300 cursor-not-allowed py-3 px-4 w-2/3 font-bold rounded-lg'
                        : 'bg-[#22AAA8] hover:bg-[#22AAA8] py-3 px-4 w-2/3 font-bold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl'"
                      style="box-shadow: 0px 20px 40px 0px #4edbd92c"
                    >
                      {{
                        canPlay(game) ? 'Играть' :
                        needsAccessExtension(game) ? 'Продлить доступ' :
                        isInCart(game) ? 'В корзине' : 'Купить игру'
                      }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- View All Games Button -->
          <!-- <div class="text-center mt-12">
            <button
              (click)="viewAllGames()"
              class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-full hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 text-lg font-medium shadow-lg"
            >
              Посмотреть все игры
            </button>
          </div> -->
        </div>
      </div>
    </section>

    <section id="how-to-play" class="relative py-24">
      <div class="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2
          class="text-4xl md:text-5xl font-black text-center uppercase text-[#40A3FF] mb-8"
        >
          как играют в Kyz Kuu
        </h2>

        <!-- Видео с летающими иконками -->
        <div class="relative flex justify-center items-center">
          <!-- Левый блок с иконками -->
          <div class="absolute left-0 top-1/2 -translate-y-1/2 space-y-4">
            <img
              src="/assets/icons/star.svg"
              class="size-20 md:size-40 animate-fly-left opacity-75"
              alt=""
              style="animation-delay: 2s"
            />
            <img
              src="/assets/icons/heart%20pink.png"
              class="size-14 md:size-44 animate-fly-left opacity-70"
              alt=""
              style="animation-delay: 4s"
            />
          </div>

          <!-- Видео -->
          <iframe
            class="z-10"
            width="1000"
            height="500"
            src="https://www.youtube.com/embed/bSSxPQEuSz0"
            title="Еркеш Хасен, Қанай - Қызық Times"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerpolicy="strict-origin-when-cross-origin"
            allowfullscreen
          ></iframe>

          <!-- Правый блок с иконками -->
          <div class="absolute right-0 top-1/2 -translate-y-1/2 space-y-4">
            <img
              src="/assets/icons/game%20blue.png"
              class="size-16 md:size-52 animate-fly-right opacity-80"
              alt=""
              style="animation-delay: 1s"
            />
            <img
              src="/assets/icons/pink-ornament2.svg"
              class="size-14 md:size-36 animate-fly-right opacity-75"
              alt=""
              style="animation-delay: 3s"
            />
          </div>
        </div>

        <!-- Подпись -->
        <div class="text-center text-white text-lg mt-5">
          Отрывок из выпуска “Кызык Times” на телеканале «Хабар». Гости передачи
          Еркеш Хасен и QANAY попробовали нашу игру.
        </div>
      </div>
    </section>



    <!-- Elegant Divider -->
    <div class="relative py-16 overflow-hidden">
      <div class="absolute inset-0 flex items-center justify-center">
        <div
          class="w-full max-w-6xl flex items-center justify-center space-x-4 opacity-30"
        >
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 0s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 0.5s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 1s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 1.5s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 2s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 2.5s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 3s"
          />
        </div>
      </div>
      <div class="relative z-10 flex justify-center">
        <!-- <div
          class="w-72 h-px bg-gradient-to-r from-transparent via-purple-400 to-transparent"
        ></div> -->
      </div>
    </div>




    <!--  divider-->
    <!-- <div class="flex items-center justify-center my-10">
      <img src="assets/icons/ornament.png" class="w-64" alt="" />
      <img src="assets/icons/ornament.png" class="w-64" alt="" />
      <img src="assets/icons/ornament.png" class="w-64" alt="" />
      <img src="assets/icons/ornament.png" class="w-64" alt="" />
      <img src="assets/icons/ornament.png" class="w-64" alt="" />
      <img src="assets/icons/ornament.png" class="w-64" alt="" />
      <img src="assets/icons/ornament.png" class="w-64" alt="" />
    </div> -->

    <!-- About Us Section -->
    <section id="about" class="relative py-24">
      <!-- Gentle floating highlights for about section -->
      <div
        class="absolute top-20 right-20 w-3 h-3 bg-cyan-400/40 rounded-full animate-sparkle"
        style="animation-delay: 2s"
      ></div>
      <div
        class="absolute bottom-20 left-20 w-4 h-4 bg-blue-300/30 rounded-full animate-sparkle"
        style="animation-delay: 4s"
      ></div>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Title -->
        <div class="text-center mb-16">
          <h2
            class="text-4xl md:text-5xl lg:text-5xl font-black text-center uppercase text-[#40A3FF] mb-6 tracking-tight"
          >
            Привет, это студия ToyForToi
          </h2>
          <div
            class="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto rounded-full"
          ></div>
        </div>

        <!-- Description -->
        <div class="text-center mb-16">
          <p
            class="text-lg md:text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed"
          >
            Мы — молодая стартап-команда из Казахстана, которая разрабатывает
            интерактивные решения для мероприятий. Мы создаём игры, которые
            украсят любой той. Мы за то, чтобы развитие технологий сохраняло
            традиции народа.
          </p>
        </div>

        <!-- Video Placeholder -->
        <div class="flex justify-center">
          <div class="relative w-full max-w-6xl">
            <div
              class="relative pb-[56.25%] h-0 overflow-hidden rounded-2xl shadow-2xl bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700"
            >
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="text-center">
                  <div
                    class="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center"
                  >
                    <svg
                      class="w-8 h-8 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"
                      />
                    </svg>
                  </div>
                  <h3 class="text-2xl md:text-3xl font-bold text-white mb-2">
                    Видео о нашей студии
                  </h3>
                  <p class="text-gray-400">
                    Скоро здесь будет интересное видео
                  </p>
                </div>
              </div>
            </div>

            <!-- Video Glow Effect -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl blur-xl -z-10 transform scale-105"
            ></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Elegant Divider -->
    <div class="relative py-16 overflow-hidden">
      <div class="absolute inset-0 flex items-center justify-center">
        <div
          class="w-full max-w-6xl flex items-center justify-center space-x-4 opacity-30"
        >
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 0s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 0.5s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 1s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 1.5s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 2s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 2.5s"
          />
          <img
            src="assets/icons/ornament.png"
            class="w-16 md:w-20 lg:w-40 animate-float"
            alt=""
            style="animation-delay: 3s"
          />
        </div>
      </div>
      <div class="relative z-10 flex justify-center">
        <!-- <div
          class="w-72 h-px bg-gradient-to-r from-transparent via-purple-400 to-transparent"
        ></div> -->
      </div>
    </div>

    <!-- Benefits Section -->
    <section class="relative py-24">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Title -->
        <div class="text-center mb-20">
          <h2
            class="text-4xl md:text-5xl lg:text-5xl font-black text-center uppercase text-[#40A3FF] mb-6 tracking-tight"
          >
            ПРЕИМУЩЕСТВА
          </h2>
          <div
            class="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto rounded-full"
          ></div>
        </div>

        <!-- Benefits Image -->
        <div class="flex justify-center">
          <div class="relative">
            <img
              src="assets/images/benefits.png"
              alt="Преимущества"
              class="max-w-full h-auto rounded-2xl shadow-2xl"
            />

            <!-- Image Glow Effect -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl blur-xl -z-10 transform scale-105"
            ></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="relative py-24">
      <!-- Subtle moving highlights for pricing section -->
      <div
        class="absolute top-16 left-1/4 w-5 h-5 bg-purple-400/20 rounded-full blur-sm animate-glow-pulse"
        style="animation-delay: 1s"
      ></div>
      <div
        class="absolute bottom-16 right-1/4 w-6 h-6 bg-blue-400/15 rounded-full blur-md animate-glow-pulse"
        style="animation-delay: 3s"
      ></div>

      <div id="pricing-title" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Title -->
        <div  class="text-center mb-20">
          <h2
            class="text-4xl md:text-5xl lg:text-5xl font-black text-center uppercase text-[#40A3FF] mb-6 tracking-tight"
          >
            Тарифы
          </h2>
          <p
            class="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
          >
            Выберите подходящий тариф для вашего мероприятия
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto mt-8 rounded-full"
          ></div>
        </div>

        <!-- Pricing Cards -->
        <div *ngIf="packagesLoading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>

        <div *ngIf="packagesError && !packagesLoading" class="text-center py-12">
          <p class="text-red-400">{{ packagesError }}</p>
        </div>

        <div
          *ngIf="!packagesLoading && !packagesError"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12"
        >
          <!-- Static "No Subscription" Card -->
          <div class="relative w-full max-w-sm mx-auto bg-cover bg-center bg-no-repeat rounded-2xl p-6 md:p-8 flex flex-col justify-between h-auto min-h-[500px] glow-on-hover transition-all duration-300 hover:scale-105 cursor-pointer"
               style="background-image: url('assets/images/rectangle.png');"
               (click)="viewGamesSection()">

            <!-- Overlay for better text readability -->
            <div class="absolute inset-0 bg-black/20 rounded-2xl"></div>

            <div class="relative z-10 text-white flex flex-col justify-between h-full">
              <!-- Plan Details -->
              <div class="space-y-6">
                <div class="text-center">
                  <h3 class="font-bold text-2xl md:text-3xl mb-2">Без подписки</h3>
                  <p class="text-lg md:text-xl text-gray-200">Разовые покупки</p>
                </div>

                <!-- Features List -->
                <div class="space-y-4 pb-16">
                  <div class="flex items-start gap-3">
                    <img src="/assets/icons/check.svg" alt="check" class="w-5 h-5 mt-0.5 flex-shrink-0">
                    <span class="text-base leading-relaxed">Формат, при котором вы платите за каждую игру отдельно.</span>
                  </div>
                  <div class="flex items-start gap-3">
                    <img src="/assets/icons/check.svg" alt="check" class="w-5 h-5 mt-0.5 flex-shrink-0">
                    <span class="text-base leading-relaxed">Вам предоставляется код доступа на 24 часа.</span>
                  </div>
                  <div class="flex items-start gap-3">
                    <img src="/assets/icons/check.svg" alt="check" class="w-5 h-5 mt-0.5 flex-shrink-0">
                    <span class="text-base leading-relaxed">Это аренда игры на определенную дату для вашего тоя.</span>
                  </div>
                </div>
              </div>

              <!-- Pricing and Action -->
              <div class="mt-8 space-y-6">
                <div class="text-center">
                  <div class="text-3xl md:text-4xl font-bold mb-1">20 000 ₸</div>
                  <div class="text-lg text-gray-300">за 1 игру</div>
                </div>

                <button
                  (click)="viewGamesSection(); $event.stopPropagation()"
                  class="w-full bg-[#22AAA8] hover:bg-[#1e9896] py-3 px-4 font-bold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
                  style="box-shadow: 0 20px 40px 0 rgba(34, 170, 168, 0.2);">
                  Выбрать игры
                </button>
              </div>
            </div>
          </div>

          <!-- Backend Packages -->
          <app-tarrifs-card
            *ngFor="let package of featuredPackages"
            [package]="package"
            [isAuthenticated]="authService.isAuthenticated()"
            (purchasePackage)="purchasePackage($event)"
            (viewDetails)="viewPackageDetails($event)"
          />
        </div>

        <!-- View All Packages Button removed - packages are only displayed on main page -->
      </div>
    </section>

    <!-- How It Works Section -->
    <section class="relative py-20">
      <div class="lg:block hidden h-fit">
        <img
          src="/assets/images/left-img.png"
          class="absolute"
          style="width: 500px"
          alt=""
        />
        <img
          src="/assets/images/right-img.png"
          class="absolute right-0"
          style="width: 500px"
          alt=""
        />
      </div>
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Title -->
        <div class="text-center mb-16">
          <h2
            class="text-4xl md:text-5xl font-black text-center uppercase text-[#40A3FF] mb-4"
          >
            КАК ЭТО РАБОТАЕТ?
          </h2>
          <p class="text-xl text-gray-300 max-w-2xl mx-auto">
            Простые шаги для незабываемого развлечения
          </p>
        </div>

        <!-- Steps -->
        <div class="space-y-12">
          <!-- Step 1 -->
          <div class="text-center group">
            <div class="relative mb-6">
              <div
                class="w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-6xl font-bold text-white shadow-lg shadow-cyan-500/50 group-hover:shadow-cyan-500/70 transition-all duration-300 transform group-hover:scale-110"
              >
                1
              </div>
              <div
                class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full opacity-20 animate-pulse"
              ></div>
            </div>
            <h3 class="text-xl font-bold text-[#31B9CC] mb-3 uppercase">
              Покупаете игру
            </h3>
            <p class="text-gray-300 text-base leading-relaxed">
              Вам предоставляется игра и ключи для активации игры.
              <br />
              Также в стоимость входят устройства (микрофоны либо контроллеры).
            </p>
          </div>
          <div class="text-center group">
            <div class="relative mb-6">
              <div
                class="w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-6xl font-bold text-white shadow-lg shadow-cyan-500/50 group-hover:shadow-cyan-500/70 transition-all duration-300 transform group-hover:scale-110"
              >
                2
              </div>
              <div
                class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full opacity-20 animate-pulse"
              ></div>
            </div>
            <h3 class="text-xl font-bold text-[#31B9CC] mb-3 uppercase">
              Активируете игру
            </h3>
            <p class="text-gray-300 text-base leading-relaxed">
              При запуске игры у вас запросит ключ активации.

              <br />
              В течение 10 часов можно сколько угодно играть и перезапускать.
              <br />
              (если у вас есть подписка, то ключ действует месяц)
            </p>
          </div>
          <div class="text-center group">
            <div class="relative mb-6">
              <div
                class="w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-6xl font-bold text-white shadow-lg shadow-cyan-500/50 group-hover:shadow-cyan-500/70 transition-all duration-300 transform group-hover:scale-110"
              >
                3
              </div>
              <div
                class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full opacity-20 animate-pulse"
              ></div>
            </div>
            <h3 class="text-xl font-bold text-[#31B9CC] mb-3 uppercase">
              играйте на тое
            </h3>
            <p class="text-gray-300 text-base leading-relaxed">
              Подключите необходимые устройства на месте проведения и
              <br />
              используйте второй ключ для активации игры.

              <br />
              Если у вас есть подписка, второй ключ не нужен.
            </p>
          </div>
          <div class="text-center group">
            <div class="relative mb-6">
              <div
                class="w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-6xl font-bold text-white shadow-lg shadow-cyan-500/50 group-hover:shadow-cyan-500/70 transition-all duration-300 transform group-hover:scale-110"
              >
                4
              </div>
              <div
                class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full opacity-20 animate-pulse"
              ></div>
            </div>
            <h3 class="text-xl font-bold text-[#31B9CC] mb-3 uppercase">
              Покупаете ключи
            </h3>
            <p class="text-gray-300 text-base leading-relaxed">
              Игра и устройства навсегда ваши. Перед следующим тоем
              <br />
              не забудьте приобрести новый ключ.
              <br />
              Если у вас подписка, то обновляете ключи раз в месяц.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="relative py-20">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Title -->
        <div class="text-center mb-16">
          <h2
            class="text-4xl md:text-5xl font-black text-center uppercase text-[#40A3FF] mb-4"
          >
            ЧАСТО ЗАДАВАЕМЫЕ ВОПРОСЫ
          </h2>

          <p class="text-xl text-gray-300 max-w-2xl mx-auto">
            Ответы на популярные вопросы о наших играх
          </p>
        </div>

        <!-- FAQ Items -->
        <div class="space-y-4">
          <div
            *ngFor="let faq of faqItems"
            class="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-2xl border border-gray-600/30 backdrop-blur-sm overflow-hidden"
          >
            <button
              (click)="toggleFaq(faq.id)"
              class="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-colors rounded-2xl"
            >
              <span class="text-lg font-semibold text-white">{{ faq.question }}</span>
              <svg
                class="w-6 h-6 text-[#40A3FF] transform transition-transform duration-300"
                [class.rotate-180]="faq.isOpen"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            <div
              class="faq-content transition-all duration-300 ease-in-out"
              [class.faq-open]="faq.isOpen"
              [class.faq-closed]="!faq.isOpen"
            >
              <div class="px-6 pb-6">
                <p class="text-gray-300 leading-relaxed">
                  {{ faq.answer }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="relative py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- Company Info -->
          <div class="col-span-1 md:col-span-2">
            <div class="text-3xl font-bold text-white mb-4">TOY FOR TOI</div>
            <p class="text-gray-400 mb-6 max-w-md">
              Создаем незабываемые моменты с помощью интерактивных игр и
              развлечений для ваших мероприятий.
            </p>
            <div class="flex space-x-4">
              <a
                href="#"
                class="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center hover:from-purple-700 hover:to-pink-700 transition-all"
              >
                <span class="text-white text-sm">f</span>
              </a>
              <a
                href="#"
                class="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center hover:from-purple-700 hover:to-pink-700 transition-all"
              >
                <span class="text-white text-sm">t</span>
              </a>
              <a
                href="#"
                class="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center hover:from-purple-700 hover:to-pink-700 transition-all"
              >
                <span class="text-white text-sm">i</span>
              </a>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h3 class="text-white font-semibold mb-4">Быстрые ссылки</h3>
            <ul class="space-y-2">
              <li>
                <a
                  (click)="scrollToTop(); $event.preventDefault()"
                  class="text-gray-400 hover:text-purple-400 transition-colors cursor-pointer"
                  >Главная</a
                >
              </li>
              <li>
                <a
                  (click)="scrollToSection('games'); $event.preventDefault()"
                  class="text-gray-400 hover:text-purple-400 transition-colors cursor-pointer"
                  >Игры</a
                >
              </li>
              <li>
                <a
                  (click)="scrollToSection('about'); $event.preventDefault()"
                  class="text-gray-400 hover:text-purple-400 transition-colors cursor-pointer"
                  >О нас</a
                >
              </li>
              <li>
                <a
                  (click)="scrollToSection('pricing'); $event.preventDefault()"
                  class="text-gray-400 hover:text-purple-400 transition-colors cursor-pointer"
                  >Контакты</a
                >
              </li>
            </ul>
          </div>

          <!-- Contact -->
          <div>
            <h3 class="text-white font-semibold mb-4">Контакты</h3>
            <ul class="space-y-2">
              <li class="text-gray-400">+7 (XXX) XXX-XX-XX</li>
              <li class="text-gray-400">info&#64;toyfortoi.com</li>
              <li class="text-gray-400">г. Алматы, Казахстан</li>
            </ul>
          </div>
        </div>

        <!-- Copyright -->
        <div class="border-t border-gray-800 mt-8 pt-8 text-center">
          <p class="text-gray-400">© 2024 TOY FOR TOI. Все права защищены.</p>
        </div>
      </div>
    </footer>
  </div>
  <!-- End content overlay -->
</div>
<!-- End main container -->

<!-- Modal Component -->
<app-modal></app-modal>
