import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { Profile } from './profile';
import { SharedModule } from "../../shared/shared.module";
import { AccessTypeModalComponent } from '../../shared/components/access-type-modal/access-type-modal.component';
import { ProfileSettingsComponent } from './components/profile-settings/profile-settings.component';
import { UsersManagementComponent } from './components/users-management/users-management.component';
import { UserDetailComponent } from './components/user-detail/user-detail.component';
import { GamesManagementComponent } from './components/games-management/games-management.component';
import { GameDetailComponent } from './components/game-detail/game-detail.component';
import { LibraryManagementComponent } from './components/library-management/library-management.component';
import { UserLibraryComponent } from './components/user-library/user-library.component';
import { GameKeysManagementComponent } from './components/game-keys-management/game-keys-management.component';
import { GameFilesManagementComponent } from './components/game-files-management/game-files-management.component';
import { GameAccessManagementComponent } from './components/game-access-management/game-access-management.component';
import { PurchaseHistoryComponent } from './components/purchase-history/purchase-history.component';
import { ProfileGamesCatalogComponent } from './components/profile-games-catalog.component';

import { ProfileGameDetailComponent } from './components/profile-game-detail.component';
import { GamePackagesManagementComponent } from './components/game-packages-management/game-packages-management.component';
import { GamePackageDetailComponent } from './components/game-package-detail/game-package-detail.component';

// Temporarily disabled - packages moved to main page only
// import { ProfilePackagesCatalogComponent } from './components/profile-packages-catalog.component';
// import { ProfilePackageDetailComponent } from './components/profile-package-detail.component';

@NgModule({
    declarations: [
        Profile,
        ProfileSettingsComponent,
        UsersManagementComponent,
        UserDetailComponent,
        GamesManagementComponent,
        GameDetailComponent,
        LibraryManagementComponent,
        UserLibraryComponent,
        GameKeysManagementComponent,
        GameFilesManagementComponent,
        GameAccessManagementComponent,
        PurchaseHistoryComponent,
        ProfileGamesCatalogComponent,

        ProfileGameDetailComponent,
        GamePackagesManagementComponent,
        GamePackageDetailComponent,
        // Temporarily disabled - packages moved to main page only
        // ProfilePackagesCatalogComponent,
        // ProfilePackageDetailComponent
    ],
    imports: [
        CommonModule,
        RouterModule,
        FormsModule,
        ReactiveFormsModule,
        SharedModule,
        AccessTypeModalComponent
    ],
    exports: [
        // Export admin components so they can be used in AdminModule
        UsersManagementComponent,
        UserDetailComponent,
        GamesManagementComponent,
        LibraryManagementComponent,
        GameFilesManagementComponent,
        GameAccessManagementComponent,
        GamePackagesManagementComponent
    ]
})
export class ProfileModule { }
