import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import { Header } from "./components/header/header";
import { Card } from './components/card/card';
import { TarrifsCard } from './components/tarrifs-card/tarrifs-card';
import { LoadingSpinner } from './components/loading-spinner/loading-spinner';
import { GlobalLoading } from './components/global-loading/global-loading';
import { ModalComponent } from './components/modal/modal.component';
import { SubscriptionPurchaseModalComponent } from './components/subscription-purchase-modal/subscription-purchase-modal.component';
import { PackagePurchaseModalComponent } from './components/package-purchase-modal/package-purchase-modal.component';

@NgModule({
    declarations: [
        Header,
        Card,
        TarrifsCard,
        LoadingSpinner,
        GlobalLoading,
        ModalComponent,
        SubscriptionPurchaseModalComponent,
        PackagePurchaseModalComponent
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule
    ],
  exports: [
    Header,
    Card,
    TarrifsCard,
    LoadingSpinner,
    GlobalLoading,
    ModalComponent,
    SubscriptionPurchaseModalComponent,
    PackagePurchaseModalComponent
  ]
})
export class SharedModule { }
