<!-- Package Purchase Modal -->
<div
  *ngIf="isVisible"
  class="modal-backdrop"
>
  <div class="modal-content-container">
    <!-- Modal Content -->
    <div 
      class="relative bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 border border-slate-600/50 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden modal-enter"
      (click)="$event.stopPropagation()"
    >
      <!-- Header -->
      <div class="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-slate-600/50 p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-xl font-bold text-white mb-1">Покупка пакета</h3>
            <p class="text-gray-300 text-sm" *ngIf="package">{{ package?.name }}</p>
          </div>
          <button
            (click)="closeModal()"
            class="text-gray-400 hover:text-white transition-colors p-2 hover:bg-slate-700/50 rounded-lg"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Modal Body -->
      <div class="p-6 overflow-y-auto max-h-[70vh]">
        
        <!-- Loading State -->
        <div *ngIf="packageLoading" class="text-center py-8">
          <div class="flex items-center justify-center space-x-2">
            <svg class="animate-spin h-8 w-8 text-blue-400" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-gray-300">Загрузка пакета...</span>
          </div>
        </div>

        <!-- Error State -->
        <div *ngIf="packageError" class="text-center py-8">
          <div class="bg-red-500/20 border border-red-500/50 rounded-lg p-4">
            <p class="text-red-300">{{ packageError }}</p>
          </div>
        </div>

        <!-- Package Content -->
        <div *ngIf="package && !packageLoading">
          
          <!-- Package Info -->
          <div class="bg-slate-700/30 rounded-lg p-6 mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 class="text-lg font-semibold text-white mb-2">{{ package?.name }}</h4>
                <p class="text-gray-300 mb-4">{{ package?.description }}</p>

                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-400">Цена:</span>
                    <span class="text-white font-medium">{{ formatPrice(package?.price || '0') }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-400">Длительность:</span>
                    <span class="text-white">{{ package?.duration_days || 0 }} дней</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-400">Максимум игр:</span>
                    <span class="text-white">{{ package?.max_selectable_games || 0 }}</span>
                  </div>
                </div>
              </div>
              
              <div class="space-y-3">
                <div class="flex items-center space-x-2">
                  <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-gray-300 text-sm">{{ package?.benefit_1 }}</span>
                </div>
                <div class="flex items-center space-x-2">
                  <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-gray-300 text-sm">{{ package?.benefit_2 }}</span>
                </div>
                <div class="flex items-center space-x-2">
                  <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-gray-300 text-sm">{{ package?.benefit_3 }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Game Selection -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-lg font-semibold text-white">Выберите {{ package?.max_selectable_games || 0 }} игр</h4>
              <span class="text-blue-400 text-sm">
                Выбрано: {{ selectedGameIds.length }} / {{ package?.max_selectable_games || 0 }}
              </span>
            </div>

            <!-- Error Message -->
            <div *ngIf="errorMessage" class="bg-red-500/20 border border-red-500/50 rounded-lg p-3 mb-4">
              <p class="text-red-300 text-sm">{{ errorMessage }}</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
              <label *ngFor="let game of (package?.games || [])"
                     class="flex items-center space-x-3 p-4 bg-slate-700/30 rounded-lg hover:bg-slate-600/30 transition-colors cursor-pointer border-2"
                     [class]="isGameSelected(game.id) ? 'border-blue-500 bg-blue-500/10' : 'border-transparent'">
                <input
                  type="checkbox"
                  [checked]="isGameSelected(game.id)"
                  (change)="onGameSelectionChange(game.id, $event)"
                  class="rounded border-slate-500 text-blue-600 focus:ring-blue-500"
                >
                <div class="flex-1">
                  <div class="text-white text-sm font-medium">{{ game?.title }}</div>
                </div>
              </label>
            </div>

            <!-- Preview Section -->
            <div *ngIf="selectedGameIds.length > 0" class="mt-4">
              <div *ngIf="previewLoading" class="bg-slate-700/30 rounded-lg p-4 text-center">
                <div class="flex items-center justify-center space-x-2">
                  <svg class="animate-spin h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-gray-300 text-sm">Обновление предварительного просмотра...</span>
                </div>
              </div>

              <div *ngIf="!previewLoading && previewData" class="bg-slate-700/30 rounded-lg p-4">
                <h5 class="text-white font-medium mb-3">Предварительный просмотр покупки</h5>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-300">Выбрано игр:</span>
                    <span class="text-white">{{ previewData.preview.games_count }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-300">Длительность:</span>
                    <span class="text-white">{{ previewData.preview.expires_in_days }} дней</span>
                  </div>
                  <div class="flex justify-between font-medium">
                    <span class="text-gray-300">Стоимость:</span>
                    <span class="text-white">{{ formatPrice(previewData.preview.total_price) }}</span>
                  </div>
                </div>
                
                <div *ngIf="!previewData.can_purchase" class="mt-3 bg-red-500/20 border border-red-500/50 rounded p-2">
                  <p class="text-red-300 text-xs">Покупка недоступна. Проверьте выбранные игры.</p>
                </div>

                <!-- Selected Games List -->
                <div *ngIf="previewData.preview.selected_games && previewData.preview.selected_games.length > 0" class="mt-4 pt-4 border-t border-slate-600">
                  <h6 class="text-white font-medium mb-2 text-sm">Выбранные игры:</h6>
                  <div class="space-y-1">
                    <div *ngFor="let game of previewData.preview.selected_games" class="text-gray-300 text-xs">
                      • {{ game.title }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="border-t border-slate-600/50 p-6 bg-slate-800/50">
        <div class="flex justify-between items-center">
          <button
            (click)="closeModal()"
            class="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-lg transition-colors text-sm"
          >
            Отмена
          </button>

          <button
            (click)="completePurchase()"
            [disabled]="!canPurchase() || purchaseLoading"
            class="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium"
          >
            <span *ngIf="!purchaseLoading">Купить пакет</span>
            <span *ngIf="purchaseLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Обработка...
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
