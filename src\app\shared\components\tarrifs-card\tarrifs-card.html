<div
  *ngIf="package"
  class="relative w-full max-w-sm mx-auto bg-cover bg-center bg-no-repeat rounded-2xl p-6 md:p-8 flex flex-col justify-between h-auto min-h-[500px] glow-on-hover transition-all duration-300 hover:scale-105 selection:"
  [class.active-subscription]="package.has_active_subscription"
  style="background-image: url('assets/images/rectangle.png')"
>
  <!-- Overlay for better text readability -->
  <div class="absolute inset-0 bg-black/20 rounded-2xl"></div>

  <div class="relative z-10 text-white flex flex-col justify-between h-full">
    <!-- Plan Details -->
    <div class="space-y-6">
      <div class="text-center">
        <h3 class="font-bold text-2xl md:text-3xl mb-2">{{ package.name }}</h3>
        <p class="text-lg md:text-xl text-gray-200">
          {{ package.description }}
        </p>

        <!-- Active Subscription Badge -->
        <div *ngIf="package.has_active_subscription" class="mt-3">
          <span
            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-600 text-white"
          >
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              ></path>
            </svg>
            Активная подписка
          </span>
        </div>
      </div>

      <!-- Features List -->
      <div class="space-y-4">
        <div *ngIf="package.benefit_1" class="flex items-start gap-3">
          <img
            src="/assets/icons/check.svg"
            alt="check"
            class="w-5 h-5 mt-0.5 flex-shrink-0"
          />
          <span class="text-base leading-relaxed">{{ package.benefit_1 }}</span>
        </div>
        <div *ngIf="package.benefit_2" class="flex items-start gap-3">
          <img
            src="/assets/icons/check.svg"
            alt="check"
            class="w-5 h-5 mt-0.5 flex-shrink-0"
          />
          <span class="text-base leading-relaxed">{{ package.benefit_2 }}</span>
        </div>
        <div *ngIf="package.benefit_3" class="flex items-start gap-3">
          <img
            src="/assets/icons/check.svg"
            alt="check"
            class="w-5 h-5 mt-0.5 flex-shrink-0"
          />
          <span class="text-base leading-relaxed">{{ package.benefit_3 }}</span>
        </div>
      </div>

      <!-- Games List -->
      <div *ngIf="package.games && package.games.length > 0" class="mt-6 pb-4">
        <h4 class="text-sm font-medium text-gray-300 mb-3">Игры в пакете:</h4>
        <div class="space-y-2 max-h-32 overflow-y-auto">
          <div
            *ngFor="let game of package.games"
            class="text-xs text-gray-400 hover:text-white cursor-pointer transition-colors duration-200 py-1 px-2 rounded hover:bg-white/10"
            (click)="onGameClick(game); $event.stopPropagation()"
          >
            {{ game.title }}
          </div>
        </div>
      </div>
    </div>

    <!-- Pricing and Action -->
    <div class="mt-8 space-y-6">
      <div class="text-center">
        <div class="text-3xl md:text-4xl font-bold mb-1">
          {{ formatPrice(package.price) }}
        </div>
        <div class="text-lg text-gray-300">
          за {{ package.duration_days }} дней
        </div>
      </div>

      <!-- Active Subscription to THIS Package Button -->
      <button
        *ngIf="isAuthenticated && package.has_active_subscription"
        (click)="onViewDetails(); $event.stopPropagation()"
        class="w-full bg-blue-600 hover:bg-blue-700 py-3 px-4 font-bold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
        style="box-shadow: 0 20px 40px 0 rgba(59, 130, 246, 0.2)"
      >
        Перейти в профиль
      </button>

      <!-- Has Subscription to Another Package Button -->
      <button
        *ngIf="isAuthenticated && !package.has_active_subscription && package.user_has_any_active_subscription"
        (click)="$event.stopPropagation()"
        class="w-full bg-orange-600 text-sm py-3 px-4 font-bold rounded-lg cursor-not-allowed opacity-75"
        disabled
      >
        Выбрана подписка на другой пакет
      </button>

      <!-- Purchase Button (No Active Subscriptions) -->
      <button
        *ngIf="isAuthenticated && !package.has_active_subscription && !package.user_has_any_active_subscription"
        (click)="onPurchasePackage(); $event.stopPropagation()"
        class="w-full bg-[#22AAA8] hover:bg-[#1e9896] py-3 px-4 font-bold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
        style="box-shadow: 0 20px 40px 0 rgba(34, 170, 168, 0.2)"
      >
        Купить за {{ formatPrice(package.price) }}
      </button>

      <!-- Login Required Button -->
      <button
        *ngIf="!isAuthenticated"
        (click)="onPurchasePackage(); $event.stopPropagation()"
        class="w-full bg-[#22AAA8] hover:bg-[#1e9896] py-3 px-4 font-bold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
        style="box-shadow: 0 20px 40px 0 rgba(34, 170, 168, 0.2)"
      >
        Купить за {{ formatPrice(package.price) }}
      </button>
    </div>
  </div>
</div>

<!-- Fallback for when no package is provided -->
<div
  *ngIf="!package"
  class="relative w-full max-w-sm mx-auto bg-cover bg-center bg-no-repeat rounded-2xl p-6 md:p-8 flex flex-col justify-between h-auto min-h-[500px] glow-on-hover transition-all duration-300 hover:scale-105"
  style="background-image: url('assets/images/rectangle.png')"
>
  <!-- Overlay for better text readability -->
  <div class="absolute inset-0 bg-black/20 rounded-2xl"></div>

  <div class="relative z-10 text-white flex flex-col justify-between h-full">
    <!-- Plan Details -->
    <div class="space-y-6">
      <div class="text-center">
        <h3 class="font-bold text-2xl md:text-3xl mb-2">BASIC</h3>
        <p class="text-lg md:text-xl text-gray-200">Разовые покупки</p>
      </div>

      <!-- Features List -->
      <div class="space-y-4">
        <div class="flex items-start gap-3">
          <img
            src="/assets/icons/check.svg"
            alt="check"
            class="w-5 h-5 mt-0.5 flex-shrink-0"
          />
          <span class="text-base leading-relaxed">2 ключа на игру</span>
        </div>
        <div class="flex items-start gap-3">
          <img
            src="/assets/icons/check.svg"
            alt="check"
            class="w-5 h-5 mt-0.5 flex-shrink-0"
          />
          <span class="text-base leading-relaxed"
            >Бесплатные устройства на купленную игру</span
          >
        </div>
      </div>
    </div>

    <!-- Pricing and Action -->
    <div class="mt-8 space-y-6">
      <div class="text-center">
        <div class="text-3xl md:text-4xl font-bold mb-1">20 000 ₸</div>
        <div class="text-lg text-gray-300">за 1 игру</div>
      </div>

      <button
        class="w-full bg-[#22AAA8] hover:bg-[#1e9896] py-3 px-4 font-bold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
        style="box-shadow: 0 20px 40px 0 rgba(34, 170, 168, 0.2)"
      >
        Купить
      </button>
    </div>
  </div>
</div>
