<!-- Games Management Content -->
<div class="max-w-6xl">
  <!-- Header -->
  <div class="flex items-center mb-4 lg:mb-6">
    <div class="w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-3 lg:mr-4">
      <svg class="w-5 h-5 lg:w-6 lg:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
      </svg>
    </div>
    <div>
      <h1 class="text-xl lg:text-2xl font-bold text-white">Управление играми</h1>
      <p class="text-gray-400 text-sm lg:text-base">Создание, редактирование и удаление игр</p>
    </div>
  </div>

  <!-- Games Management Section -->
  <div class="bg-slate-800/60 backdrop-blur-md border border-slate-600/30 rounded-xl p-4 mb-6">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-base lg:text-lg font-semibold text-white">Управление играми</h3>
      <button
        (click)="toggleAddForm()"
        [ngClass]="showAddForm ? 'from-red-600 to-red-700 hover:from-red-700 hover:to-red-800' : 'from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700'"
        class="px-3 py-2 text-sm bg-gradient-to-r text-white font-medium rounded-lg shadow-lg transition-all transform hover:scale-[1.02]">
        <svg *ngIf="!showAddForm" class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <svg *ngIf="showAddForm" class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        {{ showAddForm ? 'Отменить' : 'Добавить игру' }}
      </button>
    </div>

    <!-- Search and Filter Controls -->
    <div class="flex flex-col sm:flex-row gap-3 mb-4">
      <div class="flex-1">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="onSearchChange()"
          placeholder="Поиск игр..."
          class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
        >
      </div>
      <div class="flex gap-2">
        <select
          [(ngModel)]="sortBy"
          (change)="onSortChange()"
          class="px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:border-blue-500 transition-colors"
        >
          <option value="-created_at">Новые первые</option>
          <option value="created_at">Старые первые</option>
          <option value="title">По названию (А-Я)</option>
          <option value="-title">По названию (Я-А)</option>
          <option value="price">По цене (возр.)</option>
          <option value="-price">По цене (убыв.)</option>
        </select>
        <button
          (click)="clearFilters()"
          class="px-4 py-2 bg-slate-700/60 hover:bg-slate-600/60 text-gray-300 rounded-lg transition-colors"
        >
          Сбросить
        </button>
      </div>
    </div>

    <!-- Add Game Form -->
    <div *ngIf="showAddForm" class="bg-slate-800/60 border border-slate-600/50 rounded-lg p-6 mb-6 animate-fadeIn">
      <h4 class="text-white font-semibold text-base mb-4">Добавить новую игру</h4>

      <!-- Error Message -->
      <div *ngIf="addGameError" class="bg-red-500/20 border border-red-500/50 text-red-300 px-4 py-3 rounded-lg mb-4 text-sm">
        {{ addGameError }}
      </div>

      <form (ngSubmit)="submitAddGame()" class="space-y-4">
        <!-- Title -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Название игры *</label>
          <input
            type="text"
            [(ngModel)]="newGame.title"
            name="title"
            required
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Введите название игры">
        </div>

        <!-- Subtitle -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Подзаголовок</label>
          <input
            type="text"
            [(ngModel)]="newGame.subtitle"
            name="subtitle"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Введите подзаголовок (необязательно)">
        </div>

        <!-- Game Code -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Код игры</label>
          <input
            type="text"
            [(ngModel)]="newGame.game_code"
            name="game_code"
            maxlength="6"
            (input)="onGameCodeInput($event)"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Введите уникальный код (до 6 символов)">
          <p class="text-xs text-gray-400 mt-1">Код будет автоматически преобразован в верхний регистр</p>
        </div>

        <!-- Description -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Описание *</label>
          <textarea
            [(ngModel)]="newGame.description"
            name="description"
            required
            rows="3"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            placeholder="Введите описание игры"></textarea>
        </div>

        <!-- Price -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Цена (₸) *</label>
          <input
            type="text"
            [(ngModel)]="newGame.price"
            name="price"
            required
            pattern="[0-9]+(\.[0-9]{1,2})?"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="0.00">
        </div>

        <!-- Two column layout for checkboxes and additional fields -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Trial Available -->
          <div class="flex items-center">
            <input
              type="checkbox"
              [(ngModel)]="newGame.trial_available"
              name="trial_available"
              id="trial_available"
              class="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500 focus:ring-2">
            <label for="trial_available" class="ml-2 text-gray-300 text-sm">Пробная версия доступна</label>
          </div>

          <!-- Requires Device -->
          <div class="flex items-center">
            <input
              type="checkbox"
              [(ngModel)]="newGame.requires_device"
              name="requires_device"
              id="requires_device"
              class="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500 focus:ring-2">
            <label for="requires_device" class="ml-2 text-gray-300 text-sm">Требует устройство</label>
          </div>
        </div>

        <!-- Target Audience -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Целевая аудитория</label>
          <input
            type="text"
            [(ngModel)]="newGame.target_audience"
            name="target_audience"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Например: Дети 6-12 лет">
        </div>

        <!-- Cover Image -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Обложка игры</label>
          <input
            type="file"
            (change)="onCoverImageSelected($event)"
            accept="image/*"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white file:mr-3 file:py-1 file:px-3 file:rounded-lg file:border-0 file:text-xs file:font-medium file:bg-blue-600 file:text-white hover:file:bg-blue-700 file:cursor-pointer">
          <p class="text-gray-400 text-xs mt-1">Поддерживаются форматы: JPG, PNG, GIF. Максимальный размер: 5MB</p>
        </div>

        <!-- How to Play -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Как играть</label>
          <textarea
            [(ngModel)]="newGame.how_to_play"
            name="how_to_play"
            rows="2"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            placeholder="Инструкции по игре (необязательно)"></textarea>
        </div>

        <!-- System Requirements -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Системные требования</label>
          <textarea
            [(ngModel)]="newGame.system_requirements"
            name="system_requirements"
            rows="3"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            placeholder="Минимальные системные требования (необязательно)"></textarea>
        </div>

        <!-- Required Equipment -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Необходимое оборудование</label>
          <textarea
            [(ngModel)]="newGame.required_equipment"
            name="required_equipment"
            rows="2"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            placeholder="Список необходимого оборудования (необязательно)"></textarea>
        </div>

        <!-- Form Actions -->
        <div class="flex space-x-3 pt-3">
          <button
            type="submit"
            [disabled]="addGameLoading"
            class="flex-1 px-3 py-2 text-sm bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:from-gray-600 disabled:to-gray-700 text-white font-medium rounded-lg transition-all duration-300 disabled:cursor-not-allowed">
            <span *ngIf="!addGameLoading">Создать игру</span>
            <span *ngIf="galleryUploadLoading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Загрузка галереи...
            </span>
            <span *ngIf="addGameLoading && !galleryUploadLoading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Создание...
            </span>
          </button>

          <button
            type="button"
            (click)="toggleAddForm()"
            [disabled]="addGameLoading"
            class="px-4 py-2 text-sm lg:text-base bg-slate-600 hover:bg-slate-700 disabled:bg-slate-700 text-white font-medium rounded-lg transition-all duration-300 disabled:cursor-not-allowed">
            Отменить
          </button>
        </div>
      </form>
    </div>

    <!-- Loading State -->
    <app-loading-spinner
      *ngIf="gamesLoading"
      size="medium">
    </app-loading-spinner>

    <!-- Error State -->
    <div *ngIf="gamesError && !gamesLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
      <h4 class="text-red-300 font-semibold mb-2">Ошибка загрузки игр</h4>
      <p class="text-red-200 mb-4">{{ gamesError }}</p>
      <button (click)="loadGames()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
        Попробовать снова
      </button>
    </div>

    <!-- Games Stats -->
    <div *ngIf="!gamesLoading && !gamesError" class="mb-6">
      <p class="text-gray-400 text-sm lg:text-base">
        Показано {{ games.length }} из {{ totalGames }} игр
        <span *ngIf="searchTerm">(поиск: "{{ searchTerm }}")</span>
      </p>
    </div>

    <!-- Games Grid -->
    <div *ngIf="!gamesLoading && !gamesError" class="space-y-6">
      <!-- Games Cards Grid -->
      <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        <div *ngFor="let game of games" class="bg-slate-800/40 border border-slate-600/50 rounded-lg overflow-hidden hover:border-slate-500/60 transition-all duration-300 hover:transform hover:scale-[1.02] cursor-pointer" (click)="openGameDetail(game.id)">
          <!-- Game Cover Image -->
          <div class="h-32 bg-gradient-to-br from-slate-700 to-slate-800 relative overflow-hidden">
            <img
              *ngIf="game.cover_image"
              [src]="game.cover_image"
              [alt]="game.title"
              class="w-full h-full object-cover"
            >
            <div *ngIf="!game.cover_image" class="w-full h-full flex items-center justify-center">
              <svg class="w-16 h-16 text-slate-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
              </svg>
            </div>

            <!-- Price Badge -->
            <div class="absolute top-2 right-2 bg-green-600/90 backdrop-blur-sm text-white px-2 py-1 rounded-full text-xs font-medium">
              {{ game.price }} ₸
            </div>

            <!-- Trial Badge -->
            <div *ngIf="game.trial_available" class="absolute top-2 left-2 bg-blue-600/90 backdrop-blur-sm text-white px-2 py-1 rounded-full text-xs font-medium">
              Пробная
            </div>
          </div>

          <!-- Game Content -->
          <div class="p-3">
            <div class="mb-3">
              <h4 class="text-white font-semibold text-sm mb-1 line-clamp-1">{{ game.title }}</h4>
              <p *ngIf="game.subtitle" class="text-gray-400 text-xs mb-2 line-clamp-1">{{ game.subtitle }}</p>
            </div>

            <!-- Game Details -->
            <div class="space-y-1 mb-3">
              <div *ngIf="game.game_code" class="flex items-center text-xs text-gray-400">
                <svg class="w-3 h-3 mr-1 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span class="font-mono text-blue-400">{{ game.game_code }}</span>
              </div>

              <div class="flex items-center text-xs">
                <svg class="w-3 h-3 mr-1 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span [ngClass]="game.requires_device ? 'text-yellow-400' : 'text-gray-400'">
                  {{ game.requires_device ? 'Устройство' : 'Без устройства' }}
                </span>
              </div>
            </div>

            <!-- Action Button -->
            <div class="flex">
              <button
                (click)="openGameDetail(game.id); $event.stopPropagation()"
                class="w-full px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors font-medium">
                <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Подробнее
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="games.length === 0" class="text-center py-16">
        <svg class="w-16 h-16 text-gray-500 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
        </svg>
        <h3 class="text-gray-400 text-lg font-medium mb-2">Игры не найдены</h3>
        <p class="text-gray-500 text-sm lg:text-base">
          <span *ngIf="searchTerm">Попробуйте изменить поисковый запрос</span>
          <span *ngIf="!searchTerm">Начните с создания первой игры</span>
        </p>
      </div>

      <!-- Pagination -->
      <div *ngIf="getTotalPages() > 1" class="flex items-center justify-between pt-6 border-t border-slate-700/40">
        <div class="text-sm lg:text-base text-gray-400">
          Страница {{ currentPage }} из {{ getTotalPages() }}
        </div>

        <div class="flex items-center space-x-2">
          <!-- Previous Button -->
          <button
            (click)="previousPage()"
            [disabled]="!hasPrevious"
            [ngClass]="hasPrevious ? 'bg-slate-700 hover:bg-slate-600 text-white' : 'bg-slate-800 text-gray-500 cursor-not-allowed'"
            class="px-3 py-2 rounded-lg text-sm lg:text-base transition-colors">
            Назад
          </button>

          <!-- Page Numbers -->
          <div class="flex space-x-1">
            <button
              *ngFor="let page of getPageNumbers()"
              (click)="goToPage(page)"
              [ngClass]="page === currentPage ? 'bg-blue-600 text-white' : 'bg-slate-700 hover:bg-slate-600 text-gray-300'"
              class="px-3 py-2 rounded-lg text-sm lg:text-base transition-colors">
              {{ page }}
            </button>
          </div>

          <!-- Next Button -->
          <button
            (click)="nextPage()"
            [disabled]="!hasNext"
            [ngClass]="hasNext ? 'bg-slate-700 hover:bg-slate-600 text-white' : 'bg-slate-800 text-gray-500 cursor-not-allowed'"
            class="px-3 py-2 rounded-lg text-sm lg:text-base transition-colors">
            Далее
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Game Detail Modal -->
<app-game-detail
  [gameId]="selectedGameId"
  [isVisible]="showGameDetail"
  (close)="closeGameDetail()"
  (gameUpdated)="onGameUpdated($event)"
  (gameDeleted)="onGameDeleted($event)">
</app-game-detail>
